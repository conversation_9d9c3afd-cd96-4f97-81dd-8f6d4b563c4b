"use client";
import { useAuthContext } from "@/domain/auth/context/auth-context";

export default function BuyerDashboard() {
  const { user } = useAuthContext();
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center text-lg">
        Please log in to view your dashboard.
      </div>
    );
  }

  return (
    <section className="p-8">
      <h1 className="text-2xl font-bold mb-4">
        Welcome back, {user?.name?.split(" ")[0] || "Buyer"}
      </h1>
      <p>Here you can view your orders, wishlist, and explore more products.</p>
    </section>
  );
}
