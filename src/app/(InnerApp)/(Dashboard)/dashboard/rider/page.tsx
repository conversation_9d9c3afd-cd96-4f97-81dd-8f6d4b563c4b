"use client";
import { useAuthContext } from "@/domain/auth/context/auth-context";

export default function RiderDashboard() {
  const { user } = useAuthContext();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center text-lg">
        Please log in to view your dashboard.
      </div>
    );
  }

  return (
    <section className="p-8">
      <h1 className="text-2xl font-bold mb-4">
        Welcome back, {user?.name?.split(" ")[0] || "Buyer"} You are here as{" "}
        <span className="text-blue-500">{user?.current_role}</span>
      </h1>{" "}
      <p>View your assigned deliveries and track your earnings.</p>
    </section>
  );
}
