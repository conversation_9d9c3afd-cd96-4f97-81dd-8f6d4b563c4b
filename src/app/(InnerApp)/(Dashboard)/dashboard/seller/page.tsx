"use client";

import { useAuthContext } from "@/domain/auth/context/auth-context";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function SellerDashboard() {
  const { user } = useAuthContext();

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold">
        Welcome back, {user?.first_name || "Seller"} 👋
      </h1>

      {/*   Cards detils */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { label: "Total sales", value: "₦895,88.00" },
          { label: "Available Products", value: "65" },
          { label: "Total Orders", value: "65" },
          { label: "Customers Experience", value: "65" },
        ].map((item, index) => (
          <Card key={index} className="shadow-sm">
            <CardContent className="p-4">
              <p className="text-sm text-gray-500">{item.label}</p>
              <h2 className="text-lg font-semibold">{item.value}</h2>
            </CardContent>
          </Card>
        ))}
      </div>

      {/*  Chart Plaeholder */}
      <Card className="shadow-sm">
        <CardContent className="p-6">
          <h2 className="text-md font-semibold mb-2">Performance</h2>
          <div className="h-40 bg-gray-100 rounded-md flex items-center justify-center text-gray-400 text-sm">
            Sales / Orders Chart (this one go come later)
          </div>
        </CardContent>
      </Card>

      {/* Recent Orders Table */}
      <Card className="shadow-sm">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-md font-semibold">Recent Orders</h2>
            <a href="#" className="text-sm text-blue-500 hover:underline">
              View all
            </a>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="text-left border-b">
                  <th className="py-2">Product name</th>
                  <th className="py-2">Product ID</th>
                  <th className="py-2">Date</th>
                  <th className="py-2">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {[
                  {
                    name: "Product A",
                    id: "1233243rjc",
                    date: "05/04/2025",
                    status: "Pending",
                  },
                  {
                    name: "Product A",
                    id: "1233243rjc",
                    date: "05/04/2025",
                    status: "Delivered",
                  },
                  {
                    name: "Product A",
                    id: "1233243rjc",
                    date: "05/04/2025",
                    status: "Delivered",
                  },
                ].map((order, index) => (
                  <tr key={index}>
                    <td className="py-2">{order.name}</td>
                    <td className="py-2">{order.id}</td>
                    <td className="py-2">{order.date}</td>
                    <td className="py-2">
                      <Badge
                        variant={
                          order.status === "Delivered" ? "default" : "secondary"
                        }
                        className={
                          order.status === "Pending"
                            ? "bg-yellow-200 text-yellow-800"
                            : "bg-green-200 text-green-800"
                        }
                      >
                        {order.status}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
