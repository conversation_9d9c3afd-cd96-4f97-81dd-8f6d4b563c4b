"use client";

import { useEffect, useLayoutEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useAuthContext } from "@/domain/auth/context/auth-context";
import { toast } from "sonner";
import LoadingBars from "@/components/ui/loading-bars";
import { CheckCircle, XCircle } from "lucide-react";
import { useVerifyMagicToken } from "@/domain/auth/hooks/use-verify-magic-token";

interface MagicLinkVerifierProps {
  children: React.ReactNode;
}

export default function MagicLinkVerifier({ children }: MagicLinkVerifierProps) {
  const searchParams = useSearchParams();
  const { verifyMagicLinkToken } = useAuthContext();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const {mutate: verifyMagicLink, isPending } = useVerifyMagicToken();

  useLayoutEffect(() => {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);
    const token = params.get('access_token');
    const type = params.get('type');

    if (token && type === 'magiclink') {
      verifyMagicLink(token);
    }
  }, [searchParams]);

  // const handleMagicLinkVerification = async (token: string) => {
  //   setIsVerifying(true);
  //   setVerificationStatus('idle');

  //   try {
  //     await verifyMagicLinkToken(token);
  //     setVerificationStatus('success');

  //     toast.success("Welcome to Markket!", {
  //       description: "You have been successfully signed in.",
  //       duration: 4000,
  //     });

  //     // Clean up URL parameters
  //     const url = new URL(window.location.href);
  //     url.searchParams.delete('token');
  //     url.searchParams.delete('type');
  //     window.history.replaceState({}, '', url.toString());

  //     // Add a small delay before redirecting to show success message
  //     setTimeout(() => {
  //       // The redirect is handled in the verifyMagicLinkToken function
  //     }, 1500);

  //   } catch (error) {
  //     console.error("Magic link verification failed:", error);
  //     setVerificationStatus('error');

  //     const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

  //     toast.error("Verification failed", {
  //       description: "The magic link is invalid or has expired. Please try signing in again.",
  //       duration: 6000,
  //     });

  //     // Clean up URL parameters even on error
  //     const url = new URL(window.location.href);
  //     url.searchParams.delete('token');
  //     url.searchParams.delete('type');
  //     window.history.replaceState({}, '', url.toString());

  //     // Redirect to home after showing error for a few seconds
  //     setTimeout(() => {
  //       setVerificationStatus('idle');
  //     }, 3000);
  //   } finally {
  //     setIsVerifying(false);
  //   }
  // };

  // Show verification UI if currently verifying
  if (isPending) {
    return (
      <div className="min-h-screen relative flex flex-col items-center justify-center bg-gray-50">
        <div className="h-1 bg-[#cc3a1b] animate-pulse absolute z-100 top-0 w-full" />
        <>{children}</>
      </div>
    );
  }

  // Show success message briefly
  if (verificationStatus === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            Successfully signed in!
          </h2>
          <p className="mt-2 text-gray-600">
            Redirecting you to your dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Show error message briefly
  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <XCircle className="mx-auto h-16 w-16 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            Verification failed
          </h2>
          <p className="mt-2 text-gray-600">
            The magic link is invalid or has expired.
          </p>
          <p className="mt-1 text-sm text-gray-500">
            You will be redirected to the homepage shortly.
          </p>
        </div>
      </div>
    );
  }

  // Render normal content if not verifying
  return <>{children}</>;
}
