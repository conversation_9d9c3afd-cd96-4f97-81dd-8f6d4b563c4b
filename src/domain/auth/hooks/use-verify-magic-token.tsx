import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { setCookie, deleteCookie } from 'cookies-next';
import type { User } from '@/types/user';
import { API_BASE } from '@/lib/constants';
import { $http, addAccessTokenToHttpInstance } from '../lib/http';
import { toast } from 'sonner';

export function useVerifyMagicToken() {

  return useMutation({
    mutationFn: async (token: string) => {
        return await $http.post(`/auth/magic-link/verify`, { token });
    },
    onSuccess: (data) => {
      addAccessTokenToHttpInstance(data.data.access_token);
    },
    onError: (error) => {
      console.error("Verification error:", error);
       toast.error("Something went wrong", {
            description: "Failed to send magic link. Please try again.",
        });
    },
});


}

// try {

//   if (response.status !== 200) {
//     toast.error("Verification failed", {
//       description: "The magic link is invalid or has expired. Please try signing in again.",
//       duration: 6000,
//     });
//     throw new Error("Verification failed");
//   }
//   if (response && response.data && response.data.access_token) {
//       const access_token = response.data.access_token;
//       setCookie('markket_auth_token', access_token, {
//         secure: true,
//         sameSite: 'strict',
//         maxAge: 7 * 24 * 60 * 60, // 7 days
//       });
//   }

//   return { success: true, user: userData };
// } catch (error) {
//   deleteCookie('markket_auth_token');
//   deleteCookie('markket_user_data');

//   if (axios.isAxiosError(error)) {
//     const status = error.response?.status;
//     const message = error.response?.data?.message || error.message;

//     if (status === 400) throw new Error("Invalid magic link token");
//     if (status === 401) throw new Error("Magic link has expired");
//     if (status === 404) throw new Error("Magic link not found");
//     throw new Error(`Verification failed: ${message}`);
//   }
//   throw error;
// }