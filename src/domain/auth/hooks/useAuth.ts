import { useState, useCallback, useEffect } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import type { RegisterPayload, User, UserRole } from "@/types/user";
import { registerUser, verifyMagicLink } from "@/lib/api/auth";

const API_BASE = "https://markket-be.onrender.com/api/v1";

const setCookie = (name: string, value: string, options: {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  maxAge?: number;
  path?: string;
} = {}) => {
  const {
    httpOnly = false,
    secure = process.env.NODE_ENV === 'production',
    sameSite = 'strict',
    maxAge = 7 * 24 * 60 * 60, // 7 days in seconds
    path = '/'
  } = options;

  let cookieString = `${name}=${encodeURIComponent(value)}; Path=${path}; Max-Age=${maxAge}; SameSite=${sameSite}`;

  if (secure) {
    cookieString += '; Secure';
  }

  if (httpOnly) {
    cookieString += '; HttpOnly';
  }

  document.cookie = cookieString;
};

const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);

  if (parts.length === 2) {
    const cookieValue = parts.pop()?.split(';').shift();
    return cookieValue ? decodeURIComponent(cookieValue) : null;
  }

  return null;
};

const deleteCookie = (name: string, path: string = '/') => {
  document.cookie = `${name}=; Path=${path}; Expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=strict`;
};

const isTokenValid = (token: string): boolean => {
  if (!token) return false;

  try {
    // Basic JWT structure validation (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // Decode payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired
    if (payload.exp && payload.exp < currentTime) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
};

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const savedToken = getCookie('markket_auth_token');
        const savedUser = getCookie('markket_user_data');

        if (savedToken && isTokenValid(savedToken)) {
          setToken(savedToken);

          if (savedUser) {
            try {
              const userData = JSON.parse(savedUser);
              setUser(userData);
            } catch (error) {
              console.error('Error parsing user data from cookie:', error);
              deleteCookie('markket_user_data');
            }
          } else {
            await fetchUserProfile(savedToken);
          }
        } else {
          // Clear invalid or expired tokens
          if (savedToken) {
            deleteCookie('markket_auth_token');
            deleteCookie('markket_user_data');
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  // Fetch user profile from backend
  const fetchUserProfile = useCallback(async (authToken: string) => {
    try {
      const response = await axios.get(`${API_BASE}/auth/me`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      const userData = response.data;
      setUser(userData);

      // Store user data in secure cookie
      setCookie('markket_user_data', JSON.stringify(userData), {
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
      });

      return userData;
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      // If profile fetch fails, clear auth state
      await logout();
      throw error;
    }
  }, []);


  const logout = useCallback(async () => {
    try {
      setIsLoading(true);

      // // Call backend logout endpoint if token exists
      // if (token) {
      //   try {
      //     await axios.post(`${API_BASE}/auth/logout`, {}, {
      //       headers: {
      //         Authorization: `Bearer ${token}`,
      //       },
      //     });
      //   } catch (error) {
      //     // Continue with logout even if backend call fails
      //     console.warn('Backend logout failed:', error);
      //   }
      // }

      setUser(null);
      setToken(null);
      deleteCookie('markket_auth_token');
      deleteCookie('markket_user_data');
      router.push("/");
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [router, token]);


  const switchRole = useCallback(
    async (role: UserRole) => {
      if (!user?.roles.includes(role)) return;

      try {
        setIsLoading(true);

        // Update role on backend if possible
        if (token) {
          try {
            await axios.patch(`${API_BASE}/auth/switch-role`,
              { role },
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );
          } catch (error) {
            console.warn('Backend role switch failed:', error);
            // Continue with local update
          }
        }

        const updatedUser = { ...user, current_role: role };
        setUser(updatedUser);

        // Update user data in secure cookie
        setCookie('markket_user_data', JSON.stringify(updatedUser), {
          secure: true,
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60,
        });

        router.push("/dashboard");
      } catch (error) {
        console.error('Role switch error:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [user, router, token]
  );

  const verifyMagicLinkToken = useCallback(
    async (magicToken: string) => {
      try {
        setIsLoading(true);

        const response = await verifyMagicLink(magicToken);

        // Handle different response structures
        const responseData = response.data;
        const access_token = responseData.access_token || responseData.token;
        const userData = responseData.user || responseData.data;

        if (!access_token || !isTokenValid(access_token)) {
          throw new Error("Invalid or expired token received from server");
        }

        // Store token in secure cookie
        setCookie('markket_auth_token', access_token, {
          secure: true,
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });

        setToken(access_token);

        // Handle user data
        if (userData) {
          setUser(userData);
          setCookie('markket_user_data', JSON.stringify(userData), {
            secure: true,
            sameSite: 'strict',
            maxAge: 7 * 24 * 60 * 60,
          });
        } else {
          // Try to fetch user profile, or create mock user as fallback
          try {
            await fetchUserProfile(access_token);
          } catch (profileError) {
            const mockUser: User = {
              id: `user-${Date.now()}`,
              first_name: "User",
              email: "<EMAIL>",
              name: "User",
              avatar_url: "",
              current_role: "buyer",
              roles: ["buyer", "seller", "rider"],
              is_new_user: false,
            };
            setUser(mockUser);
            setCookie('markket_user_data', JSON.stringify(mockUser), {
              secure: true,
              sameSite: 'strict',
              maxAge: 7 * 24 * 60 * 60,
            });
          }
        }

        // Redirect to dashboard
        router.push("/dashboard");

        return { success: true, user: userData };
      } catch (error) {
        console.error("Magic link verification failed:", error);

        // Clear any partial auth state
        deleteCookie('markket_auth_token');
        deleteCookie('markket_user_data');
        setToken(null);
        setUser(null);

        // Provide more specific error messages
        if (axios.isAxiosError(error)) {
          const status = error.response?.status;
          const message = error.response?.data?.message || error.message;

          if (status === 400) {
            throw new Error("Invalid magic link token");
          } else if (status === 401) {
            throw new Error("Magic link has expired");
          } else if (status === 404) {
            throw new Error("Magic link not found");
          } else {
            throw new Error(`Verification failed: ${message}`);
          }
        }

        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [router, fetchUserProfile]
  );

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!token || !isTokenValid(token)) return;

    const refreshToken = async () => {
      try {
        const response = await axios.post(`${API_BASE}/auth/refresh`, {}, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const newToken = response.data.access_token;
        if (newToken && isTokenValid(newToken)) {
          setCookie('markket_auth_token', newToken, {
            secure: true,
            sameSite: 'strict',
            maxAge: 7 * 24 * 60 * 60,
          });
          setToken(newToken);
        }
      } catch (error) {
        console.warn('Token refresh failed:', error);
        // If refresh fails, logout user
        await logout();
      }
    };

    // Refresh token 5 minutes before expiration
    const tokenPayload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = tokenPayload.exp * 1000; // Convert to milliseconds
    const refreshTime = expirationTime - (5 * 60 * 1000); // 5 minutes before
    const timeUntilRefresh = refreshTime - Date.now();

    if (timeUntilRefresh > 0) {
      const refreshTimeout = setTimeout(refreshToken, timeUntilRefresh);
      return () => clearTimeout(refreshTimeout);
    }
  }, [token, logout]);

  return {
    user,
    token,
    isAuthenticated: !!user && !!token && isTokenValid(token),
    isLoading,
    isInitialized,
    logout,
    switchRole,
    verifyMagicLinkToken,
    fetchUserProfile,
  };
}
