import { API_BASE } from "@/lib/constants";
import axios, { AxiosInstance } from "axios";

const $http = axios.create({
  baseURL: API_BASE,
  headers: {
    "Content-Type": "application/json",
  },
});

$http.interceptors.request.use(
    response => response,
    (error) => {
      return Promise.reject(error);
    }
  );
  
  const addAccessTokenToHttpInstance = (token: string) => {
    $http.interceptors.request.use(
      config => {
        config.headers['Authorization'] = `Bearer ${token}`;
        return config;
      },
      error => {
        return Promise.reject(error);
      },
    );
  };
  

export {$http, addAccessTokenToHttpInstance};
